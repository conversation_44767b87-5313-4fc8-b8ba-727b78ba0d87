# NutriPro Database Schema

## Overview

The NutriPro database is built on PostgreSQL via Supabase, designed to handle both retail and wholesale operations with offline synchronization capabilities.

## Schema Design Principles

1. **Unified Data Model**: Single schema serves retail and wholesale operations
2. **Audit Trail**: All tables include created_at, updated_at, and change tracking
3. **Soft Deletes**: Use is_active flags instead of hard deletes
4. **Offline Support**: Sync tracking fields for offline-capable clients
5. **Extensibility**: JSONB fields for flexible data storage

## Core Tables

### Categories
```sql
CREATE TABLE categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar(100) NOT NULL,
  description text,
  parent_id uuid REFERENCES categories(id),
  sort_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_categories_parent ON categories(parent_id);
CREATE INDEX idx_categories_active ON categories(is_active);
```

### Products
```sql
CREATE TABLE products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sku varchar(50) UNIQUE NOT NULL,
  barcode varchar(50),
  name varchar(200) NOT NULL,
  description text,
  category_id uuid REFERENCES categories(id),
  brand_id uuid REFERENCES brands(id),
  
  -- Pricing
  retail_price decimal(10,2) NOT NULL,
  wholesale_price decimal(10,2),
  cost_price decimal(10,2),
  
  -- Inventory
  stock_quantity integer DEFAULT 0,
  min_stock_level integer DEFAULT 0,
  max_stock_level integer,
  
  -- Pricing Structure (all in AWG unless specified)
  retail_price decimal(10,2) NOT NULL, -- Customer price in AWG
  landing_cost decimal(10,2), -- Total cost including shipping (AWG)
  purchase_price decimal(10,2), -- Vendor cost in vendor's currency
  purchase_currency varchar(3) DEFAULT 'AWG', -- ISO currency code
  wholesale_price decimal(10,2), -- Wholesale price in AWG
  wholesale_available boolean DEFAULT false,

  -- Product Details
  notes text,
  weight decimal(8,2), -- in grams
  dimensions jsonb, -- {length, width, height}
  images text[], -- Array of image URLs

  -- Supplement-specific fields
  serving_size varchar(50),
  servings_per_container integer,
  ingredients text[],
  allergens text[],
  expiry_tracking boolean DEFAULT false,

  -- Product Variants
  has_variants boolean DEFAULT false,
  variant_type varchar(50), -- e.g., "Flavor", "Size", "Strength"

  -- Wholesale specific
  min_order_quantity integer DEFAULT 1,
  
  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand_id);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_stock ON products(stock_quantity);
CREATE INDEX idx_products_wholesale ON products(wholesale_available);
```

### Product Variants
```sql
CREATE TABLE product_variants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  parent_product_id uuid REFERENCES products(id) ON DELETE CASCADE,

  -- Variant Identification
  sku varchar(50) UNIQUE NOT NULL,
  barcode varchar(50),
  variant_name varchar(200) NOT NULL, -- e.g., "Chocolate", "Large", "25mg"
  variant_option varchar(100) NOT NULL, -- The specific option value

  -- Pricing (all in AWG, inherits from parent if not specified)
  retail_price decimal(10,2),
  landing_cost decimal(10,2),
  purchase_price decimal(10,2),
  purchase_currency varchar(3), -- Inherits from brand's vendor
  wholesale_price decimal(10,2),
  wholesale_available boolean,

  -- Inventory
  stock_quantity integer DEFAULT 0,
  min_stock_level integer DEFAULT 0,

  -- Variant Details
  notes text,
  images text[], -- Variant-specific images

  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_product_variants_parent ON product_variants(parent_product_id);
CREATE INDEX idx_product_variants_sku ON product_variants(sku);
CREATE INDEX idx_product_variants_barcode ON product_variants(barcode);
CREATE INDEX idx_product_variants_active ON product_variants(is_active);
```

### Currency Management
```sql
CREATE TABLE currencies (
  code varchar(3) PRIMARY KEY, -- ISO 4217 currency codes
  name varchar(100) NOT NULL,
  symbol varchar(10) NOT NULL,
  decimal_places integer DEFAULT 2,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Insert common currencies
INSERT INTO currencies (code, name, symbol) VALUES
('AWG', 'Aruban Florin', 'ƒ'),
('USD', 'US Dollar', '$'),
('EUR', 'Euro', '€'),
('GBP', 'British Pound', '£'),
('CAD', 'Canadian Dollar', 'C$');
```

### Exchange Rates
```sql
CREATE TABLE exchange_rates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  from_currency varchar(3) REFERENCES currencies(code),
  to_currency varchar(3) REFERENCES currencies(code),
  rate decimal(10,6) NOT NULL,
  effective_date date NOT NULL,
  source varchar(50) DEFAULT 'manual', -- manual, api, bank

  created_at timestamptz DEFAULT now(),

  UNIQUE(from_currency, to_currency, effective_date)
);

-- Indexes
CREATE INDEX idx_exchange_rates_currencies ON exchange_rates(from_currency, to_currency);
CREATE INDEX idx_exchange_rates_date ON exchange_rates(effective_date);

-- Function to get latest exchange rate
CREATE OR REPLACE FUNCTION get_exchange_rate(from_curr varchar(3), to_curr varchar(3))
RETURNS decimal(10,6) AS $$
DECLARE
  rate decimal(10,6);
BEGIN
  -- Return 1.0 if same currency
  IF from_curr = to_curr THEN
    RETURN 1.0;
  END IF;

  -- Get latest rate
  SELECT er.rate INTO rate
  FROM exchange_rates er
  WHERE er.from_currency = from_curr
    AND er.to_currency = to_curr
  ORDER BY er.effective_date DESC
  LIMIT 1;

  -- If no direct rate, try inverse
  IF rate IS NULL THEN
    SELECT (1.0 / er.rate) INTO rate
    FROM exchange_rates er
    WHERE er.from_currency = to_curr
      AND er.to_currency = from_curr
    ORDER BY er.effective_date DESC
    LIMIT 1;
  END IF;

  RETURN COALESCE(rate, 1.0);
END;
$$ LANGUAGE plpgsql;
```

### Customers
```sql
CREATE TABLE customers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_number varchar(20) UNIQUE,
  customer_type customer_type_enum NOT NULL,

  -- Basic Info
  first_name varchar(100),
  last_name varchar(100),
  company_name varchar(200),
  email varchar(255),
  phone varchar(20),

  -- Address
  address_line1 varchar(200),
  address_line2 varchar(200),
  city varchar(100),
  state varchar(50),
  postal_code varchar(20),
  country varchar(50) DEFAULT 'US',

  -- Coach Program
  assigned_coach_id uuid REFERENCES coaches(id),
  coach_assigned_date timestamptz,

  -- Wholesale specific
  wholesale_discount decimal(5,2) DEFAULT 0, -- percentage
  credit_limit decimal(10,2) DEFAULT 0,
  payment_terms integer DEFAULT 30, -- days
  tax_exempt boolean DEFAULT false,

  -- Preferences
  preferred_contact_method varchar(20) DEFAULT 'email',
  marketing_consent boolean DEFAULT false,

  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Custom Types
CREATE TYPE customer_type_enum AS ENUM ('retail', 'wholesale');

-- Indexes
CREATE INDEX idx_customers_type ON customers(customer_type);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_coach ON customers(assigned_coach_id);
CREATE INDEX idx_customers_active ON customers(is_active);
```

### Transactions
```sql
CREATE TABLE transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_number varchar(20) UNIQUE NOT NULL,
  
  -- References
  customer_id uuid REFERENCES customers(id),
  staff_id uuid REFERENCES auth.users(id),
  device_id uuid REFERENCES devices(id),
  
  -- Transaction Details
  transaction_type transaction_type_enum NOT NULL,
  status transaction_status_enum DEFAULT 'pending',
  
  -- Financial
  subtotal decimal(10,2) NOT NULL,
  tax_rate decimal(5,4) DEFAULT 0,
  tax_amount decimal(10,2) DEFAULT 0,
  discount_amount decimal(10,2) DEFAULT 0,
  total_amount decimal(10,2) NOT NULL,
  
  -- Payment
  payment_method varchar(50),
  payment_reference varchar(100),
  
  -- Additional Info
  notes text,
  internal_notes text,
  
  -- Offline Sync
  synced_at timestamptz,
  sync_version integer DEFAULT 1,
  
  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Custom Types
CREATE TYPE transaction_type_enum AS ENUM ('sale', 'return', 'wholesale_order', 'adjustment');
CREATE TYPE transaction_status_enum AS ENUM ('pending', 'completed', 'cancelled', 'refunded', 'partial_refund');

-- Indexes
CREATE INDEX idx_transactions_customer ON transactions(customer_id);
CREATE INDEX idx_transactions_staff ON transactions(staff_id);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_transactions_sync ON transactions(synced_at);
```

### Transaction Items
```sql
CREATE TABLE transaction_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id uuid REFERENCES transactions(id) ON DELETE CASCADE,
  product_id uuid REFERENCES products(id),
  
  -- Item Details
  quantity integer NOT NULL CHECK (quantity > 0),
  unit_price decimal(10,2) NOT NULL,
  discount_amount decimal(10,2) DEFAULT 0,
  line_total decimal(10,2) NOT NULL,
  
  -- Product snapshot (for historical accuracy)
  product_name varchar(200) NOT NULL,
  product_sku varchar(50) NOT NULL,
  
  -- Batch tracking (for supplements)
  batch_number varchar(50),
  expiry_date date,
  
  created_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_transaction_items_transaction ON transaction_items(transaction_id);
CREATE INDEX idx_transaction_items_product ON transaction_items(product_id);
```

## Coach Program Tables

### Coaches
```sql
CREATE TABLE coaches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  coach_number varchar(20) UNIQUE,

  -- Personal Info
  first_name varchar(100) NOT NULL,
  last_name varchar(100) NOT NULL,
  email varchar(255) UNIQUE NOT NULL,
  phone varchar(20),

  -- Address
  address_line1 varchar(200),
  address_line2 varchar(200),
  city varchar(100),
  state varchar(50),
  postal_code varchar(20),
  country varchar(50) DEFAULT 'US',

  -- Coach Program Details
  certification_level varchar(50),
  certification_date date,
  specializations text[],

  -- Credit System (all amounts in AWG)
  monthly_credit_amount decimal(10,2) DEFAULT 0, -- Amount reset each month
  current_credit_balance decimal(10,2) DEFAULT 0,
  last_credit_reset timestamptz, -- When credits were last reset
  total_credits_earned decimal(10,2) DEFAULT 0,
  total_credits_used decimal(10,2) DEFAULT 0,

  -- Referral Settings
  referral_fee_percentage decimal(5,2) DEFAULT 0, -- percentage of client purchases
  referral_fee_flat decimal(10,2) DEFAULT 0, -- flat fee per transaction

  -- Status
  is_active boolean DEFAULT true,
  registration_date timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_coaches_email ON coaches(email);
CREATE INDEX idx_coaches_active ON coaches(is_active);
CREATE INDEX idx_coaches_registration ON coaches(registration_date);
```

### Coach Transactions
```sql
CREATE TABLE coach_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  coach_id uuid REFERENCES coaches(id),
  customer_id uuid REFERENCES customers(id),
  transaction_id uuid REFERENCES transactions(id),

  -- Transaction Details
  transaction_type coach_transaction_type_enum NOT NULL,
  amount decimal(10,2) NOT NULL,
  description text,

  -- Credit/Referral Tracking
  credit_balance_before decimal(10,2),
  credit_balance_after decimal(10,2),

  created_at timestamptz DEFAULT now()
);

CREATE TYPE coach_transaction_type_enum AS ENUM ('credit_issued', 'credit_used', 'referral_earned', 'referral_paid');

-- Indexes
CREATE INDEX idx_coach_transactions_coach ON coach_transactions(coach_id);
CREATE INDEX idx_coach_transactions_customer ON coach_transactions(customer_id);
CREATE INDEX idx_coach_transactions_date ON coach_transactions(created_at);
```

## Vendor & Brand Management Tables

### Vendors
```sql
CREATE TABLE vendors (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vendor_code varchar(20) UNIQUE,

  -- Company Info
  company_name varchar(200) NOT NULL,
  contact_person varchar(100),
  email varchar(255),
  phone varchar(20),
  website varchar(255),

  -- Address
  address_line1 varchar(200),
  address_line2 varchar(200),
  city varchar(100),
  state varchar(50),
  postal_code varchar(20),
  country varchar(50) DEFAULT 'US',

  -- Business Details
  tax_id varchar(50),
  payment_terms integer DEFAULT 30, -- days
  credit_limit decimal(10,2) DEFAULT 0,
  discount_percentage decimal(5,2) DEFAULT 0,
  minimum_order_amount decimal(10,2) DEFAULT 0,
  preferred_currency varchar(3) DEFAULT 'USD', -- ISO currency code

  -- Communication Preferences
  preferred_order_method varchar(50) DEFAULT 'email', -- email, fax, portal, phone
  order_email varchar(255),
  order_phone varchar(20),

  -- Performance Metrics
  average_delivery_days integer,
  reliability_score decimal(3,2), -- 0.00 to 5.00
  last_order_date timestamptz,

  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_vendors_company ON vendors(company_name);
CREATE INDEX idx_vendors_active ON vendors(is_active);
CREATE INDEX idx_vendors_code ON vendors(vendor_code);
```

### Brands
```sql
CREATE TABLE brands (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  brand_code varchar(20) UNIQUE,
  brand_name varchar(100) NOT NULL,
  vendor_id uuid REFERENCES vendors(id),

  -- Brand Details
  description text,
  logo_url varchar(500),
  website varchar(255),

  -- Business Terms
  minimum_order_amount decimal(10,2) DEFAULT 0,
  discount_tier_1_threshold decimal(10,2),
  discount_tier_1_percentage decimal(5,2),
  discount_tier_2_threshold decimal(10,2),
  discount_tier_2_percentage decimal(5,2),

  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_brands_vendor ON brands(vendor_id);
CREATE INDEX idx_brands_name ON brands(brand_name);
CREATE INDEX idx_brands_active ON brands(is_active);
```

## Purchasing System Tables

### Purchase Orders
```sql
CREATE TABLE purchase_orders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  po_number varchar(20) UNIQUE NOT NULL,
  vendor_id uuid REFERENCES vendors(id),

  -- Order Details
  status po_status_enum DEFAULT 'draft',
  order_date timestamptz DEFAULT now(),
  expected_delivery_date date,
  actual_delivery_date date,

  -- Financial (in vendor's preferred currency)
  currency varchar(3) NOT NULL DEFAULT 'USD',
  subtotal decimal(10,2) NOT NULL DEFAULT 0,
  tax_amount decimal(10,2) DEFAULT 0,
  shipping_cost decimal(10,2) DEFAULT 0,
  discount_amount decimal(10,2) DEFAULT 0,
  total_amount decimal(10,2) NOT NULL DEFAULT 0,

  -- AWG equivalent (for reporting)
  total_amount_awg decimal(10,2),
  exchange_rate decimal(10,6) DEFAULT 1.0,

  -- Shipping Info
  shipping_address jsonb,
  tracking_number varchar(100),
  shipping_method varchar(50),

  -- Communication
  notes text,
  internal_notes text,
  vendor_confirmation_number varchar(100),

  -- Staff
  created_by uuid REFERENCES auth.users(id),
  approved_by uuid REFERENCES auth.users(id),
  received_by uuid REFERENCES auth.users(id),

  -- Timestamps
  approved_at timestamptz,
  sent_at timestamptz,
  received_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE TYPE po_status_enum AS ENUM ('draft', 'pending_approval', 'approved', 'sent', 'partially_received', 'received', 'cancelled');

-- Indexes
CREATE INDEX idx_purchase_orders_vendor ON purchase_orders(vendor_id);
CREATE INDEX idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX idx_purchase_orders_date ON purchase_orders(order_date);
CREATE INDEX idx_purchase_orders_po_number ON purchase_orders(po_number);
```

### Purchase Order Items
```sql
CREATE TABLE purchase_order_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  purchase_order_id uuid REFERENCES purchase_orders(id) ON DELETE CASCADE,
  product_id uuid REFERENCES products(id),

  -- Order Details
  quantity_ordered integer NOT NULL CHECK (quantity_ordered > 0),
  quantity_received integer DEFAULT 0,
  unit_cost decimal(10,2) NOT NULL,
  line_total decimal(10,2) NOT NULL,

  -- Product Info (snapshot for historical accuracy)
  product_name varchar(200) NOT NULL,
  product_sku varchar(50) NOT NULL,

  -- Receiving Details
  batch_number varchar(50),
  expiry_date date,
  received_date timestamptz,

  created_at timestamptz DEFAULT now()
);

-- Indexes
CREATE INDEX idx_po_items_po ON purchase_order_items(purchase_order_id);
CREATE INDEX idx_po_items_product ON purchase_order_items(product_id);
```

### Inventory Forecasting Tables
```sql
CREATE TABLE demand_forecasts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id),

  -- Forecast Period
  forecast_date date NOT NULL,
  forecast_period forecast_period_enum NOT NULL,

  -- Forecast Data
  predicted_demand integer NOT NULL,
  confidence_score decimal(3,2), -- 0.00 to 1.00

  -- AI Model Info
  model_version varchar(20),
  factors_considered jsonb, -- seasonality, trends, promotions, etc.

  -- Actual vs Predicted (filled after period ends)
  actual_demand integer,
  accuracy_score decimal(3,2),

  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE TYPE forecast_period_enum AS ENUM ('daily', 'weekly', 'monthly', 'quarterly');

-- Indexes
CREATE INDEX idx_demand_forecasts_product ON demand_forecasts(product_id);
CREATE INDEX idx_demand_forecasts_date ON demand_forecasts(forecast_date);
CREATE INDEX idx_demand_forecasts_period ON demand_forecasts(forecast_period);
```

### Reorder Recommendations
```sql
CREATE TABLE reorder_recommendations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id),
  vendor_id uuid REFERENCES vendors(id),

  -- Recommendation Details
  recommended_quantity integer NOT NULL,
  recommended_order_date date NOT NULL,
  urgency_level urgency_enum DEFAULT 'normal',

  -- AI Analysis
  reason_code varchar(50), -- low_stock, forecast_demand, seasonal, promotion
  confidence_score decimal(3,2),
  cost_benefit_analysis jsonb,

  -- Status
  status recommendation_status_enum DEFAULT 'pending',
  reviewed_by uuid REFERENCES auth.users(id),
  reviewed_at timestamptz,
  action_taken varchar(100), -- ordered, ignored, modified

  created_at timestamptz DEFAULT now(),
  expires_at timestamptz
);

CREATE TYPE urgency_enum AS ENUM ('low', 'normal', 'high', 'critical');
CREATE TYPE recommendation_status_enum AS ENUM ('pending', 'reviewed', 'accepted', 'rejected', 'expired');

-- Indexes
CREATE INDEX idx_reorder_recommendations_product ON reorder_recommendations(product_id);
CREATE INDEX idx_reorder_recommendations_status ON reorder_recommendations(status);
CREATE INDEX idx_reorder_recommendations_urgency ON reorder_recommendations(urgency_level);
CREATE INDEX idx_reorder_recommendations_date ON reorder_recommendations(recommended_order_date);
```

## Inventory Management Tables

### Inventory Adjustments
```sql
CREATE TABLE inventory_adjustments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id),
  staff_id uuid REFERENCES auth.users(id),
  
  adjustment_type adjustment_type_enum NOT NULL,
  quantity_change integer NOT NULL, -- positive or negative
  reason varchar(200),
  notes text,
  
  -- Cost tracking
  unit_cost decimal(10,2),
  total_cost decimal(10,2),
  
  created_at timestamptz DEFAULT now()
);

CREATE TYPE adjustment_type_enum AS ENUM ('restock', 'damage', 'theft', 'expired', 'count_correction', 'return');

-- Indexes
CREATE INDEX idx_inventory_adjustments_product ON inventory_adjustments(product_id);
CREATE INDEX idx_inventory_adjustments_date ON inventory_adjustments(created_at);
```

### Stock Movements
```sql
CREATE TABLE stock_movements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id),
  
  movement_type movement_type_enum NOT NULL,
  quantity integer NOT NULL,
  reference_id uuid, -- transaction_id or adjustment_id
  reference_type varchar(50),
  
  -- Stock levels after movement
  stock_before integer NOT NULL,
  stock_after integer NOT NULL,
  
  created_at timestamptz DEFAULT now()
);

CREATE TYPE movement_type_enum AS ENUM ('sale', 'return', 'adjustment', 'restock');

-- Indexes
CREATE INDEX idx_stock_movements_product ON stock_movements(product_id);
CREATE INDEX idx_stock_movements_date ON stock_movements(created_at);
```

## System Tables

### Devices
```sql
CREATE TABLE devices (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  device_name varchar(100) NOT NULL,
  device_type device_type_enum NOT NULL,
  device_identifier varchar(200) UNIQUE, -- MAC address or unique ID
  
  -- Sync tracking
  last_sync timestamptz,
  sync_version integer DEFAULT 1,
  
  -- Configuration
  settings jsonb DEFAULT '{}',
  
  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE TYPE device_type_enum AS ENUM ('pos_tablet', 'sales_tablet', 'delivery_mobile', 'admin_web');

-- Indexes
CREATE INDEX idx_devices_type ON devices(device_type);
CREATE INDEX idx_devices_active ON devices(is_active);
```

### Sync Queue
```sql
CREATE TABLE sync_queue (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id uuid REFERENCES devices(id),
  
  -- Operation details
  table_name varchar(100) NOT NULL,
  record_id uuid NOT NULL,
  operation sync_operation_enum NOT NULL,
  data jsonb,
  
  -- Sync status
  status sync_status_enum DEFAULT 'pending',
  retry_count integer DEFAULT 0,
  error_message text,
  
  -- Timestamps
  created_at timestamptz DEFAULT now(),
  synced_at timestamptz,
  next_retry_at timestamptz
);

CREATE TYPE sync_operation_enum AS ENUM ('insert', 'update', 'delete');
CREATE TYPE sync_status_enum AS ENUM ('pending', 'synced', 'failed', 'conflict');

-- Indexes
CREATE INDEX idx_sync_queue_device ON sync_queue(device_id);
CREATE INDEX idx_sync_queue_status ON sync_queue(status);
CREATE INDEX idx_sync_queue_retry ON sync_queue(next_retry_at) WHERE status = 'failed';
```

### User Roles & Permissions
```sql
CREATE TABLE user_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  role user_role_enum NOT NULL,

  -- Role-specific permissions
  permissions jsonb DEFAULT '{}',

  -- Status
  is_active boolean DEFAULT true,
  assigned_by uuid REFERENCES auth.users(id),
  assigned_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

CREATE TYPE user_role_enum AS ENUM ('admin', 'staff', 'accountant');

-- Indexes
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role);
CREATE INDEX idx_user_roles_active ON user_roles(is_active);

-- Ensure one active role per user
CREATE UNIQUE INDEX idx_user_roles_unique_active ON user_roles(user_id)
WHERE is_active = true;
```

## Configuration Tables

### Payment Methods
```sql
CREATE TABLE payment_methods (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar(50) NOT NULL,
  type payment_method_type_enum NOT NULL,
  is_active boolean DEFAULT true,
  sort_order integer DEFAULT 0,
  
  -- Configuration
  requires_reference boolean DEFAULT false,
  settings jsonb DEFAULT '{}',
  
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE TYPE payment_method_type_enum AS ENUM ('cash', 'card', 'check', 'store_credit', 'other');

-- Default payment methods
INSERT INTO payment_methods (name, type, sort_order) VALUES
('Cash', 'cash', 1),
('Credit Card', 'card', 2),
('Debit Card', 'card', 3),
('Check', 'check', 4);
```

### Tax Rates
```sql
CREATE TABLE tax_rates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar(100) NOT NULL,
  rate decimal(5,4) NOT NULL, -- e.g., 0.0875 for 8.75%
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Ensure only one default tax rate
CREATE UNIQUE INDEX idx_tax_rates_default ON tax_rates(is_default) WHERE is_default = true;
```

## Row Level Security (RLS) Policies

### Enable RLS on all tables
```sql
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transaction_items ENABLE ROW LEVEL SECURITY;
-- ... enable for all tables
```

### User Roles and Policies
```sql
-- Admin: Full access to everything
CREATE POLICY admin_all_access ON products FOR ALL TO authenticated
USING (auth.jwt() ->> 'role' = 'admin');

-- Staff: Read/write access to operational data
CREATE POLICY staff_products_access ON products FOR ALL TO authenticated
USING (auth.jwt() ->> 'role' IN ('admin', 'manager', 'staff'));

-- Wholesale clients: Read-only access to their data
CREATE POLICY wholesale_customer_access ON customers FOR SELECT TO authenticated
USING (
  auth.jwt() ->> 'role' = 'wholesale_client'
  AND id = (auth.jwt() ->> 'customer_id')::uuid
);

-- Wholesale clients can only see their own transactions
CREATE POLICY wholesale_transactions_access ON transactions FOR SELECT TO authenticated
USING (
  auth.jwt() ->> 'role' = 'wholesale_client'
  AND customer_id = (auth.jwt() ->> 'customer_id')::uuid
);
```

## Database Functions and Triggers

### Auto-update timestamps
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables with updated_at
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- ... apply to all relevant tables
```

### Stock movement tracking
```sql
CREATE OR REPLACE FUNCTION track_stock_movement()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert stock movement record when stock changes
  IF OLD.stock_quantity != NEW.stock_quantity THEN
    INSERT INTO stock_movements (
      product_id,
      movement_type,
      quantity,
      stock_before,
      stock_after
    ) VALUES (
      NEW.id,
      'adjustment',
      NEW.stock_quantity - OLD.stock_quantity,
      OLD.stock_quantity,
      NEW.stock_quantity
    );
  END IF;

  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER track_product_stock_changes
  AFTER UPDATE ON products
  FOR EACH ROW EXECUTE FUNCTION track_stock_movement();
```

### Generate transaction numbers
```sql
CREATE OR REPLACE FUNCTION generate_transaction_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.transaction_number IS NULL THEN
    NEW.transaction_number := 'TXN-' || to_char(now(), 'YYYYMMDD') || '-' ||
                             LPAD(nextval('transaction_number_seq')::text, 4, '0');
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE SEQUENCE transaction_number_seq;

CREATE TRIGGER generate_transaction_number_trigger
  BEFORE INSERT ON transactions
  FOR EACH ROW EXECUTE FUNCTION generate_transaction_number();
```

## Views for Common Queries

### Product inventory view
```sql
CREATE VIEW product_inventory AS
SELECT
  p.id,
  p.sku,
  p.name,
  p.stock_quantity,
  p.min_stock_level,
  CASE
    WHEN p.stock_quantity <= p.min_stock_level THEN 'low'
    WHEN p.stock_quantity = 0 THEN 'out'
    ELSE 'normal'
  END as stock_status,
  c.name as category_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.is_active = true;
```

### Sales summary view
```sql
CREATE VIEW daily_sales_summary AS
SELECT
  DATE(created_at) as sale_date,
  COUNT(*) as transaction_count,
  SUM(total_amount) as total_sales,
  SUM(tax_amount) as total_tax,
  AVG(total_amount) as average_sale
FROM transactions
WHERE status = 'completed'
  AND transaction_type = 'sale'
GROUP BY DATE(created_at)
ORDER BY sale_date DESC;
```

## Data Migration Considerations

### Loyverse Data Mapping
```sql
-- Products migration mapping
-- Loyverse -> NutriPro
-- item_id -> sku (generate if missing)
-- item_name -> name
-- price -> retail_price
-- cost -> cost_price
-- track_stock -> (always true)
-- current_stock -> stock_quantity
```

### Zoho Books Data Mapping
```sql
-- Customers migration mapping
-- Zoho Books -> NutriPro
-- contact_id -> customer_number
-- contact_name -> company_name (for wholesale)
-- contact_type -> customer_type ('wholesale')
-- credit_limit -> credit_limit
-- payment_terms -> payment_terms
```

## Performance Optimization

### Recommended Indexes
```sql
-- Composite indexes for common queries
CREATE INDEX idx_transactions_customer_date ON transactions(customer_id, created_at);
CREATE INDEX idx_products_category_active ON products(category_id, is_active);
CREATE INDEX idx_stock_movements_product_date ON stock_movements(product_id, created_at);

-- Partial indexes for specific conditions
CREATE INDEX idx_low_stock_products ON products(stock_quantity)
  WHERE stock_quantity <= min_stock_level AND is_active = true;

CREATE INDEX idx_pending_sync ON sync_queue(created_at)
  WHERE status = 'pending';
```

### Query Optimization Tips
1. Use `LIMIT` for paginated results
2. Filter by `is_active` early in queries
3. Use date ranges for transaction queries
4. Leverage partial indexes for status-based queries

---

*Document Version: 1.0*
*Last Updated: 2025-07-07*
*Next Review: Upon completion of MVP specifications*
